import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import ShopifyImage from '../ShopifyImage'
import Carousel from 'react-native-snap-carousel';
import { heightPixel, widthPixel } from '../../styles';
import Ripple from 'react-native-material-ripple';



const NewArrivalImageSlider = ({ carouselRef, productList1, windowWidth, activeIndex, onCarouselImageChange, isTapTriggered, key }) => {

    const CarouselRenderItem = ({ item, index }) => {
        const isActive = index == activeIndex
        return (
            <>

                <Ripple
                    onPress={() => {
                        isTapTriggered.current = true;
                        onCarouselImageChange(index);
                    }}
                    style={[{
                        width: widthPixel(105),
                        height: heightPixel(100),
                        borderRadius: widthPixel(9),
                        borderWidth: 1,
                        borderColor: isActive ? "black" : "#a6a6a6",
                    }]}
                >

                    <ShopifyImage
                        source={{
                            uri: item?.productDetails?.media?.edges?.[0]?.node?.image?.src
                        }}
                        style={{ height: "100%", width: "100%", borderRadius: widthPixel(9), backgroundColor: "#F0F0F0" }}
                        resizeMode={"contain"}
                    />

                </Ripple>

            </>

        )

    }

    return (
        <View>
            <Carousel
                ref={carouselRef}
                data={productList1}
                key={key}
                renderItem={CarouselRenderItem}
                sliderWidth={windowWidth}
                itemWidth={widthPixel(110)}
                style={{ gap: 10, backgroundColor: 'red' }}
                firstItem={activeIndex}
                enableMomentum={false}
                activeSlideAlignment={'start'}
                inactiveSlideScale={1}
                lockScrollWhileSnapping={false}
                decelerationRate="fast"
                inactiveSlideOpacity={1}
                onSnapToItem={(index) => {
                    onCarouselImageChange(index);
                }}
                contentContainerCustomStyle={{
                    paddingLeft: widthPixel(40),
                    backgroundColor: "transparent"
                }}
                scrollEventThrottle={16}
                scrollViewProps={{
                    bounces: false,
                    removeClippedSubviews: true,
                }}

            />
        </View>
    )
}

export default NewArrivalImageSlider

const styles = StyleSheet.create({})