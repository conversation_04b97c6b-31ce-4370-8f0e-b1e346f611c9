import React from 'react';
import { Dimensions } from 'react-native';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Text,
  Pressable,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { fonts, heightPixel, widthPixel } from '../../styles';
const { width, height } = Dimensions.get('window');

const HomeFilterButton = ({
  activeCategory,
  setActiveCategory,
  handleCategoryButtonClick,
  attributes,
  boxbackgroundColor,
  borderColor,
  backgroundColor,
  paddingVertical,
  paddingHorizontal,
  margin,
  marginHorizontal,
  buttonTextColor,
  activeButtonTextColor,
  buttonBackgroundColor,
  activeButtonBackgroundColor,
  buttonpaddingHorizontal,
  buttonpaddingVertical,
  fontFamily,
  isGradientBackground = false,
  isWhiteBg = false,
}) => {
  // console.log(attributes,  " attributes")
  const data1 = attributes?.tag1?.toLowerCase();
  const data2 = attributes?.tag2?.toLowerCase();

  if (isGradientBackground)
    return (
      <View style={[styles.container, { boxbackgroundColor }]}>
        <View
          style={[
            styles.buttons,
            {
              borderColor,
              backgroundColor: isWhiteBg ? 'white' : '#ffffff17',

            },
          ]}>

          {attributes?.tag1 && (
            <>
              {activeCategory === data1 ? (
                <LinearGradient
                  start={{ x: 1, y: 1 }} // Top-left corner
                  end={{ x: 0, y: 0 }}
                  colors={['#221f20', '#505050']}
                  style={{ borderRadius: widthPixel(5) }}>
                  <Pressable
                    style={[
                      styles.button,

                      {
                        backgroundColor: buttonBackgroundColor,
                      },
                      activeCategory === data1 &&
                      {
                        // backgroundColor: activeButtonBackgroundColor,
                      },
                    ]}
                    onPress={() => handleCategoryButtonClick(data1)}>
                    <Text
                      style={[
                        styles.buttonText,
                        { color: '#000', fontFamily },
                        activeCategory === data1 && { color: '#fff' },
                      ]}>
                      {attributes?.tag1}
                    </Text>
                  </Pressable>
                </LinearGradient>
              ) : (
                <Pressable
                  style={[
                    styles.button,

                    {
                      backgroundColor: buttonBackgroundColor,
                    },
                    activeCategory === data1 &&
                    {
                      // backgroundColor: activeButtonBackgroundColor,
                    },
                  ]}
                  onPress={() => handleCategoryButtonClick(data1)}>
                  <Text
                    style={[
                      styles.buttonText,
                      { color: '#000', fontFamily },
                      activeCategory === data1 && { color: '#fff' },
                    ]}>
                    {attributes?.tag1}
                  </Text>
                </Pressable>
              )}
            </>
          )}

          {attributes?.tag2 && (
            <>
              {activeCategory === data2 ? (
                <LinearGradient
                  start={{ x: 1, y: 1 }} // Top-left corner
                  end={{ x: 0, y: 0 }}
                  colors={['#221f20', '#505050']}
                  style={{ borderRadius: widthPixel(5) }}>
                  <Pressable
                    style={[
                      styles.button,
                      { backgroundColor: buttonBackgroundColor },
                      activeCategory === data2 &&
                      {
                        // backgroundColor: activeButtonBackgroundColor,
                      },
                    ]}
                    onPress={() => handleCategoryButtonClick(data2)}>
                    <Text
                      style={[
                        styles.buttonText,
                        { color: '#000', fontFamily },
                        activeCategory === data2 && { color: '#fff' },
                      ]}>
                      {attributes?.tag2}
                    </Text>
                  </Pressable>
                </LinearGradient>
              ) : (
                <Pressable
                  style={[
                    styles.button,
                    { backgroundColor: buttonBackgroundColor },
                    activeCategory === data2 &&
                    {
                      // backgroundColor: activeButtonBackgroundColor,
                    },
                  ]}
                  onPress={() => handleCategoryButtonClick(data2)}>
                  <Text
                    style={[
                      styles.buttonText,
                      { color: '#000', fontFamily },
                      activeCategory === data2 && { color: '#fff' },
                    ]}>
                    {attributes?.tag2}
                  </Text>
                </Pressable>
              )}
            </>

          )}


        </View>
      </View>
    );

  return (
    <View style={[styles.container, { boxbackgroundColor }]}>

      <View style={{ borderWidth: 1, borderColor: borderColor, borderRadius: widthPixel(6) }} >
        <View
          style={[
            styles.buttons,
            {
              borderColor,
              backgroundColor: isWhiteBg ? 'white' : '#ffffff17',
            },
            isWhiteBg && { borderColor: 'white' },
          ]}>
          <Pressable
            style={[
              styles.button,

              {
                backgroundColor: buttonBackgroundColor,
              },
              activeCategory === data1 && {
                backgroundColor: activeButtonBackgroundColor,
              },
            ]}
            onPress={() => handleCategoryButtonClick(data1)}>
            <Text
              style={[
                styles.buttonText,
                { color: buttonTextColor, fontFamily },
                activeCategory === data1 && { color: activeButtonTextColor },
              ]}>
              {attributes?.tag1}
            </Text>
          </Pressable>

          <Pressable
            style={[
              styles.button,
              { backgroundColor: buttonBackgroundColor },
              activeCategory === data2 && {
                backgroundColor: activeButtonBackgroundColor,
              },
            ]}
            onPress={() => handleCategoryButtonClick(data2)}>
            <Text
              style={[
                styles.buttonText,
                { color: buttonTextColor, fontFamily },
                activeCategory === data2 && { color: activeButtonTextColor },
              ]}>
              {attributes?.tag2}
            </Text>
          </Pressable>
        </View>

      </View>
    </View>
  );
};

export default HomeFilterButton;

const styles = StyleSheet.create({
  container: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttons: {
    backgroundColor: '#ffffff17',
    flexDirection: 'row',
    justifyContent: 'center',
    borderRadius: widthPixel(5),
    borderWidth: widthPixel(1),
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: heightPixel(3),
    paddingHorizontal: widthPixel(3),

  },
  button: {
    width: widthPixel(56),
    borderRadius: widthPixel(5),
    paddingVertical: heightPixel(4),
    // paddingVertical:hp('0.5%'),
    borderWidth: 0,
  },
  buttonText: {
    fontSize: fonts._10,
    textAlign: 'center',
  },
});
