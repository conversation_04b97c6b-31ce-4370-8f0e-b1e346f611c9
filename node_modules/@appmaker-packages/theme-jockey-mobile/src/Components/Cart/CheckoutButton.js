import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Ripple from 'react-native-material-ripple';
import { handleAction } from '@appmaker-xyz/react-native';
import { useCart, useUser } from '@appmaker-xyz/shopify';
import { fonts, heightPixel, widthPixel } from '../../styles';
import LinearGradient from 'react-native-linear-gradient';
import { currencyHelper } from '@appmaker-xyz/shopify';

const CheckoutButton = (props) => {
  const { openCheckout, cartTotalPayableWithCurrency } = useCart(props);
  const { isLoggedin } = useUser();
  // const price = currencyHelper(props?.blockData?.totalPrice?.amount, props?.blockData?.totalPrice?.currencyCode);

  // console.log("CheckoutButton::", price);


  return (
    <View style={styles.container}>
      <View style={styles.amountContainer}>
        <Text style={styles.amountText}>{cartTotalPayableWithCurrency}</Text>
        <Text style={styles.incl}>(Incl. Of All Taxes)</Text>
      </View>

      <LinearGradient
        start={{ x: 1, y: 1 }} // Top-left corner
        end={{ x: 0, y: 0 }}
        colors={['#221f20', '#505050']}
        style={styles.button}>
        <Ripple
          onPress={() => {
            if (isLoggedin) {
              openCheckout();
            } else {
              handleAction({
                action: 'OPEN_INAPP_PAGE',
                pageId: 'LoginOptions',
                params: {
                  isFromCart: true,
                },
              });
            }
          }}
          style={{
            width: '100%',
            height: '100%',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <Text style={styles.buttonText}>PLACE ORDER</Text>
        </Ripple>
      </LinearGradient>
    </View>
  );
};

export default CheckoutButton;

const styles = StyleSheet.create({
  container: {
    paddingVertical: heightPixel(30),
    flexDirection: 'row',
    justifyContent: 'center',
    backgroundColor: '#fff',
    alignItems: 'center',
    borderTopWidth: 0.5,
    borderTopColor: '#cfcfcf',
  },
  amountContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  amountText: {
    fontSize: fonts._16,
    fontFamily: fonts.FONT_FAMILY.Medium,
    fontWeight: '600',
  },
  incl: {
    fontSize: fonts._11,
    fontFamily: fonts.FONT_FAMILY.Regular,
  },
  buttonContainer: {
    flex: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: fonts._16,
    fontFamily: fonts.FONT_FAMILY.Regular,
  },
  button: {
    flex: 1.5,
    justifyContent: 'center',
    alignItems: 'center',
    height: heightPixel(46),
    marginHorizontal: widthPixel(20),
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: widthPixel(12),
  },
});
