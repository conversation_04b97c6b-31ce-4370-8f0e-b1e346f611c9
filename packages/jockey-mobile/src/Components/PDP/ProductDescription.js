import React, { useState, useEffect } from 'react';
import {
  View,
  Image,
  Text,
  StyleSheet,
  Dimensions,
  Modal,
  ScrollView,
  Pressable,
} from 'react-native';
import Svg, { Path } from 'react-native-svg';
import RenderHTML from 'react-native-render-html';
import ImageViewer from 'react-native-image-zoom-viewer';
import SwiperFlatList from 'react-native-swiper-flatlist';
import { fetchData } from './ProductDetails';
import FastImage from 'react-native-fast-image';
import { fonts, heightPixel, widthPixel } from '../../styles';
import ShopifyImage from '../ShopifyImage';

const { width, height } = Dimensions.get('window');
const slideWidth = width * 0.65; // Adjust slide width to 70% of the screen
const itemSpacing = widthPixel(8); // Add spacing between the slides

const ProductDescription = ({ description, descriptionImage }) => {
  const [isModalVisible, setModalVisible] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [images, setImages] = useState([]);

  if (descriptionImage?.value) {
    const data = JSON.parse(descriptionImage?.value).map((value) =>
      value.trim(),
    );

    fetchData(data)
      .then((result) => setImages(result.data.nodes))
      .catch((error) => console.log(error, ' errorrrrrrrrrrrrrrrrrrrrrrrr'));
  }

  const newImages =
    images.length > 0 &&
    images.map((value) => ({
      url: value.image.src,
    }));

  const openModal = (index) => {
    setCurrentIndex(index);
    setModalVisible(true);
  };

  const closeModal = () => {
    setModalVisible(false);
  };

  return (
    <>
      <Text style={styles.title}>Product Description</Text>
      {images.length > 0 && (
        <>
          <ScrollView
            horizontal
            style={{ paddingVertical: widthPixel(16) }}
            showsHorizontalScrollIndicator={false}>
            {images.map((item, index) => {
              return (
                <Pressable key={index} onPress={() => openModal(index)}>
                  <View style={styles.slide}>
                    <ShopifyImage
                      source={{
                        uri: item?.image?.src
                      }}
                      style={styles.image}
                      maxWidth={200}
                    />
                  </View>
                </Pressable>
              );
            })}
          </ScrollView>

          <Modal
            visible={isModalVisible}
            transparent={true}
            onRequestClose={closeModal}>
            <View style={styles.modalOverlay}>
              <Pressable
                style={styles.modalBackdrop}
                onPress={closeModal}
                activeOpacity={1}
              />

              <View style={styles.modalContainer}>
                <Pressable
                  style={styles.closeButton}
                  onPress={closeModal}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                >
                  <Svg width="30" height="26" viewBox="0 0 16 16" fill="#fff">
                    <Path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M8 8.707L11.646 12.354l.708-.707L8.707 8l3.647-3.646-.707-.708L8 7.293 4.354 3.646l-.707.708L7.293 8l-3.646 3.646.707.708L8 8.707z"
                    />
                  </Svg>
                </Pressable>

                <View style={styles.imageViewerContainer}>
                  <ImageViewer
                    backgroundColor="transparent"
                    imageUrls={newImages}
                    index={currentIndex}
                    onSwipeDown={closeModal}
                    onChange={(index) => setCurrentIndex(index)}
                    enableSwipeDown={true}
                    renderIndicator={() => null}
                    onCancel={closeModal}
                  />
                </View>
              </View>
            </View>
          </Modal>
        </>
      )}
      {description && (
        <View style={{ marginTop: heightPixel(16), width: '100%' }}>
          <RenderHTML
            contentWidth={width}
            source={{ html: description }}
            tagsStyles={{
              p: {
                fontSize: fonts._14,
                color: '#221f20cc',
                lineHeight: heightPixel(19),
                fontFamily: fonts.FONT_FAMILY.Regular,
              },
            }}
            baseStyle={{
              fontFamily: fonts.FONT_FAMILY.Regular, // Replace with your font name
              fontSize: fonts._16,
              color: '#333',
            }}
          />
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  title: {
    fontSize: fonts._16,
    textAlign: 'left',
    color: '#221f20',
    lineHeight: heightPixel(23),
    fontFamily: fonts.FONT_FAMILY.SemiBold,
    // marginBottom: heightPixel(10),
  },
  container: {
    flex: 1,
    // height: heightPixel(530),
    width: '100%',
    height: '100%',
    // alignItems: 'center',
  },
  slide: {
    width: 270,
    justifyContent: 'center',
    alignItems: 'center',
    height: 320,
    marginRight: 16,
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: widthPixel(10),
    // resizeMode: 'contain',
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: heightPixel(10),
  },
  dot: {
    width: widthPixel(8),
    height: heightPixel(8),
    borderRadius: widthPixel(4),
    backgroundColor: '#221f205e',
    marginHorizontal: widthPixel(4),
  },
  activeDot: {
    width: widthPixel(13),
    height: heightPixel(13),
    borderRadius: widthPixel(6),
    borderWidth: 1,
    borderColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: widthPixel(4),
  },
  activeDotInner: {
    width: widthPixel(6),
    height: widthPixel(6),
    borderRadius: widthPixel(3),
    backgroundColor: '#000',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: '#1e1e1ee6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
  },
  modalContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  closeButton: {
    position: 'absolute',
    top: -50,
    right: 0,
    zIndex: 999,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    padding: widthPixel(8),
    borderRadius: widthPixel(20),
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  imageViewerContainer: {
    height: height * 0.65,
    width: width * 0.85,
  },
});

export default ProductDescription;
